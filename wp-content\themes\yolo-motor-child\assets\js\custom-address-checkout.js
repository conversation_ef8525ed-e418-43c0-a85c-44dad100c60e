/**
 * Custom Address Checkout JavaScript
 * Handles address dropdown functionality on checkout page
 */

var customAddressLoading = 0;

jQuery(document).ready(function($) {
    // Handle address dropdown changes
    jQuery(document).on('change', '.custom_address_select_menu', customOnAddressSelect);
    
    // Load default addresses on page load
    customLoadDefaultAddresses();
    
    // Re-initialize dropdowns after checkout updates
    jQuery(document).on('updated_checkout', function() {
        jQuery('.custom_address_select_menu').selectWoo({
            width: 'resolve',
            containerCssClass: "custom-address-select-menu-container",
            dropdownCssClass: "custom-address-select-menu-dropdown"
        });
    });
});

/**
 * Load default addresses on page load
 */
function customLoadDefaultAddresses() {
    var types = ['billing', 'shipping'];
    
    for (var i = 0; i < types.length; i++) {
        var dropdown = jQuery('#custom_address_select_menu_' + types[i]);
        if (dropdown.length && dropdown.val() != null && dropdown.val() != 'none') {
            dropdown.trigger('change');
        }
    }
}

/**
 * Handle address selection change
 */
function customOnAddressSelect(event) {
    if (event.target.value == 'none') {
        return;
    }
    
    var formType = jQuery(event.currentTarget).data('type');
    
    // Handle "Add new" option
    if (event.target.value == 'add_new') {
        console.log('Add one time address selected for:', formType);
        customResetCheckoutInputFields(formType);
        customEnableFormFields(formType);

        // Trigger checkout update after clearing fields
        setTimeout(function() {
            jQuery('body').trigger('update_checkout');
        }, 500);
        return;
    }
    
    // Load address data via AJAX
    customLoadAddressData(event.target.value, formType);
}

/**
 * Reset checkout input fields
 */
function customResetCheckoutInputFields(formType) {
    var fields = [
        formType + '_first_name',
        formType + '_last_name',
        formType + '_company',
        formType + '_address_1',
        formType + '_address_2',
        formType + '_city',
        formType + '_postcode',
        formType + '_state',
        formType + '_country'
    ];

    console.log('Clearing fields for form type:', formType);

    fields.forEach(function(fieldName) {
        var field = jQuery('#' + fieldName);
        if (field.length) {
            console.log('Clearing field:', fieldName);
            field.val('').trigger('change');
        } else {
            console.log('Field not found:', fieldName);
        }
    });

    // Also clear any select2 dropdowns
    fields.forEach(function(fieldName) {
        var field = jQuery('#' + fieldName);
        if (field.length && field.hasClass('select2-hidden-accessible')) {
            field.val('').trigger('change.select2');
        }
    });
}

/**
 * Enable form fields if they were disabled
 */
function customEnableFormFields(formType) {
    console.log('Enabling form fields for:', formType);

    if (formType == 'shipping') {
        jQuery('.woocommerce-shipping-fields__field-wrapper input, .woocommerce-shipping-fields__field-wrapper span, .woocommerce-shipping-fields__field-wrapper select')
            .css('pointer-events', 'auto')
            .css('background', '')
            .prop('disabled', false);

        // Also enable specific shipping fields
        jQuery('#shipping_first_name, #shipping_last_name, #shipping_company, #shipping_address_1, #shipping_address_2, #shipping_city, #shipping_postcode, #shipping_state, #shipping_country')
            .css('pointer-events', 'auto')
            .css('background', '')
            .prop('disabled', false);

    } else if (formType == 'billing') {
        jQuery('.woocommerce-billing-fields__field-wrapper input, .woocommerce-billing-fields__field-wrapper span, .woocommerce-billing-fields__field-wrapper select')
            .css('pointer-events', 'auto')
            .css('background', '')
            .prop('disabled', false);

        // Also enable specific billing fields
        jQuery('#billing_first_name, #billing_last_name, #billing_company, #billing_address_1, #billing_address_2, #billing_city, #billing_postcode, #billing_state, #billing_country')
            .css('pointer-events', 'auto')
            .css('background', '')
            .prop('disabled', false);
    }

    console.log('Form fields enabled for:', formType);
}

/**
 * Load address data via AJAX
 */
function customLoadAddressData(addressId, formType) {
    customAddressLoading++;
    
    // Show loading state
    customShowLoadingState(formType);
    
    var formData = new FormData();
    formData.append('action', 'custom_get_address_by_id');
    formData.append('address_id', addressId);
    formData.append('security_token', customAddress.security_token);
    
    // Fallback for AJAX URL if customAddress is not defined
    var ajaxUrl = (typeof customAddress !== 'undefined' && customAddress.ajaxurl) ?
                  customAddress.ajaxurl :
                  (typeof ajaxurl !== 'undefined' ? ajaxurl : '/wp-admin/admin-ajax.php');

    var securityToken = (typeof customAddress !== 'undefined' && customAddress.security_token) ?
                        customAddress.security_token :
                        'fallback_token';

    console.log('Using AJAX URL:', ajaxUrl);
    console.log('Using security token:', securityToken);

    // Update the security token in form data
    formData.set('security_token', securityToken);

    $.ajax({
        url: ajaxUrl,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            customAddressLoading--;
            customHideLoadingState(formType);
            
            if (response.success && response.data) {
                customPopulateAddressFields(response.data, formType);
            } else {
                console.error('Failed to load address:', response.data);
            }
            
            // Trigger checkout update
            if (customAddressLoading === 0) {
                jQuery('body').trigger('update_checkout');
            }
        },
        error: function(xhr, status, error) {
            customAddressLoading--;
            customHideLoadingState(formType);
            console.error('AJAX error:', error);
            
            if (customAddressLoading === 0) {
                jQuery('body').trigger('update_checkout');
            }
        }
    });
}

/**
 * Populate address fields with loaded data
 */
function customPopulateAddressFields(addressData, formType) {
    console.log('Populating fields for form type:', formType);
    console.log('Address data received:', addressData);

    // Map of address data keys to form field IDs - handle both shipping and billing
    var fieldMapping = {
        'shipping_first_name': formType + '_first_name',
        'shipping_last_name': formType + '_last_name',
        'shipping_company': formType + '_company',
        'shipping_address_1': formType + '_address_1',
        'shipping_address_2': formType + '_address_2',
        'shipping_city': formType + '_city',
        'shipping_postcode': formType + '_postcode',
        'shipping_state': formType + '_state',
        'shipping_country': formType + '_country',
        // Also handle billing fields if formType is billing
        'billing_first_name': formType + '_first_name',
        'billing_last_name': formType + '_last_name',
        'billing_company': formType + '_company',
        'billing_address_1': formType + '_address_1',
        'billing_address_2': formType + '_address_2',
        'billing_city': formType + '_city',
        'billing_postcode': formType + '_postcode',
        'billing_state': formType + '_state',
        'billing_country': formType + '_country'
    };

    // First, clear all fields
    customResetCheckoutInputFields(formType);

    // Populate each field
    Object.keys(fieldMapping).forEach(function(dataKey) {
        // Only process fields that match the current form type
        if ((formType === 'shipping' && dataKey.startsWith('shipping_')) ||
            (formType === 'billing' && dataKey.startsWith('billing_'))) {

            var fieldId = fieldMapping[dataKey];
            var fieldValue = addressData[dataKey] || '';
            var field = jQuery('#' + fieldId);

            console.log('Processing field:', dataKey, '-> ', fieldId, 'Value:', fieldValue);

            if (field.length && fieldValue) {
                field.val(fieldValue);
                console.log('Set field', fieldId, 'to:', fieldValue);

                // Trigger change event for select fields (country/state)
                if (field.is('select')) {
                    field.trigger('change');
                }
            }
        }
    });

    // Handle country change specially to update states
    var countryValue = addressData[formType + '_country'] || addressData['shipping_country'];
    var stateValue = addressData[formType + '_state'] || addressData['shipping_state'];

    if (countryValue) {
        var countryField = jQuery('#' + formType + '_country');
        console.log('Setting country field:', formType + '_country', 'to:', countryValue);

        if (countryField.length) {
            countryField.val(countryValue).trigger('change');

            // Set state after a delay to allow country change to process
            setTimeout(function() {
                if (stateValue) {
                    var stateField = jQuery('#' + formType + '_state');
                    console.log('Setting state field:', formType + '_state', 'to:', stateValue);
                    stateField.val(stateValue).trigger('change');
                }
            }, 1000); // Increased delay to ensure country processing completes
        }
    }

    // Trigger checkout update after population
    setTimeout(function() {
        jQuery('body').trigger('update_checkout');
    }, 1500);
}

/**
 * Show loading state
 */
function customShowLoadingState(formType) {
    var container = jQuery('.custom_address_selector_container');
    if (container.length) {
        container.addClass('loading');
        container.append('<div class="custom-address-loading">Loading address...</div>');
    }
}

/**
 * Hide loading state
 */
function customHideLoadingState(formType) {
    var container = jQuery('.custom_address_selector_container');
    if (container.length) {
        container.removeClass('loading');
        container.find('.custom-address-loading').remove();
    }
}
