/**
 * Custom Address Checkout JavaScript
 * Handles address dropdown functionality on checkout page
 */

var customAddressLoading = 0;

jQuery(document).ready(function($) {
    // Handle address dropdown changes
    $(document).on('change', '.custom_address_select_menu', customOnAddressSelect);
    
    // Load default addresses on page load
    customLoadDefaultAddresses();
    
    // Re-initialize dropdowns after checkout updates
    $(document).on('updated_checkout', function() {
        $('.custom_address_select_menu').selectWoo({
            width: 'resolve',
            containerCssClass: "custom-address-select-menu-container",
            dropdownCssClass: "custom-address-select-menu-dropdown"
        });
    });
});

/**
 * Load default addresses on page load
 */
function customLoadDefaultAddresses() {
    var types = ['billing', 'shipping'];
    
    for (var i = 0; i < types.length; i++) {
        var dropdown = $('#custom_address_select_menu_' + types[i]);
        if (dropdown.length && dropdown.val() != null && dropdown.val() != 'none') {
            dropdown.trigger('change');
        }
    }
}

/**
 * Handle address selection change
 */
function customOnAddressSelect(event) {
    if (event.target.value == 'none') {
        return;
    }
    
    var formType = $(event.currentTarget).data('type');
    
    // Handle "Add new" option
    if (event.target.value == 'add_new') {
        customResetCheckoutInputFields(formType);
        customEnableFormFields(formType);
        return;
    }
    
    // Load address data via AJAX
    customLoadAddressData(event.target.value, formType);
}

/**
 * Reset checkout input fields
 */
function customResetCheckoutInputFields(formType) {
    var fields = [
        formType + '_first_name',
        formType + '_last_name', 
        formType + '_company',
        formType + '_address_1',
        formType + '_address_2',
        formType + '_city',
        formType + '_postcode',
        formType + '_state',
        formType + '_country'
    ];
    
    fields.forEach(function(fieldName) {
        var field = $('#' + fieldName);
        if (field.length) {
            field.val('').trigger('change');
        }
    });
}

/**
 * Enable form fields if they were disabled
 */
function customEnableFormFields(formType) {
    if (formType == 'shipping') {
        $('.woocommerce-shipping-fields__field-wrapper input, .woocommerce-shipping-fields__field-wrapper span, .woocommerce-shipping-fields__field-wrapper select')
            .css('pointer-events', 'auto')
            .css('background', '');
    } else if (formType == 'billing') {
        $('.woocommerce-billing-fields__field-wrapper input, .woocommerce-billing-fields__field-wrapper span, .woocommerce-billing-fields__field-wrapper select')
            .css('pointer-events', 'auto')
            .css('background', '');
    }
}

/**
 * Load address data via AJAX
 */
function customLoadAddressData(addressId, formType) {
    customAddressLoading++;
    
    // Show loading state
    customShowLoadingState(formType);
    
    var formData = new FormData();
    formData.append('action', 'custom_get_address_by_id');
    formData.append('address_id', addressId);
    formData.append('security_token', customAddress.security_token);
    
    $.ajax({
        url: customAddress.ajaxurl,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            customAddressLoading--;
            customHideLoadingState(formType);
            
            if (response.success && response.data) {
                customPopulateAddressFields(response.data, formType);
            } else {
                console.error('Failed to load address:', response.data);
            }
            
            // Trigger checkout update
            if (customAddressLoading === 0) {
                $('body').trigger('update_checkout');
            }
        },
        error: function(xhr, status, error) {
            customAddressLoading--;
            customHideLoadingState(formType);
            console.error('AJAX error:', error);
            
            if (customAddressLoading === 0) {
                $('body').trigger('update_checkout');
            }
        }
    });
}

/**
 * Populate address fields with loaded data
 */
function customPopulateAddressFields(addressData, formType) {
    // Map of address data keys to form field IDs
    var fieldMapping = {
        'shipping_first_name': formType + '_first_name',
        'shipping_last_name': formType + '_last_name',
        'shipping_company': formType + '_company',
        'shipping_address_1': formType + '_address_1',
        'shipping_address_2': formType + '_address_2',
        'shipping_city': formType + '_city',
        'shipping_postcode': formType + '_postcode',
        'shipping_state': formType + '_state',
        'shipping_country': formType + '_country'
    };
    
    // Populate each field
    Object.keys(fieldMapping).forEach(function(dataKey) {
        var fieldId = fieldMapping[dataKey];
        var fieldValue = addressData[dataKey] || '';
        var field = $('#' + fieldId);
        
        if (field.length) {
            field.val(fieldValue);
            
            // Trigger change event for select fields (country/state)
            if (field.is('select')) {
                field.trigger('change');
            }
        }
    });
    
    // Handle country change specially to update states
    if (addressData.shipping_country) {
        var countryField = $('#' + formType + '_country');
        if (countryField.length) {
            countryField.val(addressData.shipping_country).trigger('change');
            
            // Set state after a short delay to allow country change to process
            setTimeout(function() {
                if (addressData.shipping_state) {
                    $('#' + formType + '_state').val(addressData.shipping_state).trigger('change');
                }
            }, 500);
        }
    }
}

/**
 * Show loading state
 */
function customShowLoadingState(formType) {
    var container = $('.custom_address_selector_container');
    if (container.length) {
        container.addClass('loading');
        container.append('<div class="custom-address-loading">Loading address...</div>');
    }
}

/**
 * Hide loading state
 */
function customHideLoadingState(formType) {
    var container = $('.custom_address_selector_container');
    if (container.length) {
        container.removeClass('loading');
        container.find('.custom-address-loading').remove();
    }
}
